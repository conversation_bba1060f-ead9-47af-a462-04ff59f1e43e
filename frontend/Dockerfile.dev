FROM node:22-alpine

WORKDIR /app

# Install curl for health checks
RUN apk add --no-cache curl

# Copy package.json and package-lock.json
COPY app/package*.json ./

# Upgrade npm to latest version and install dependencies
RUN npm install -g npm@11.4.2 && npm install

# Copy the rest of the application
COPY app/ .

# Expose port for Vite dev server
EXPOSE 3000

# Start development server
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "3000"]
