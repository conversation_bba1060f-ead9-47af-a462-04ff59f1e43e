<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1752258959662" clover="3.2.0">
  <project timestamp="1752258959662" name="All files">
    <metrics statements="6288" coveredstatements="5242" conditionals="19" coveredconditionals="7" methods="79" coveredmethods="5" elements="6386" coveredelements="5254" complexity="0" loc="6288" ncloc="6288" packages="8" files="37" classes="37"/>
    <package name="src">
      <metrics statements="50" coveredstatements="21" conditionals="4" coveredconditionals="2" methods="2" coveredmethods="0"/>
      <file name="App.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/App.vue">
        <metrics statements="22" coveredstatements="21" conditionals="2" coveredconditionals="2" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="16" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="17" count="2" type="stmt"/>
        <line num="19" count="2" type="stmt"/>
        <line num="20" count="2" type="stmt"/>
        <line num="21" count="2" type="stmt"/>
        <line num="23" count="2" type="stmt"/>
        <line num="24" count="2" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
      </file>
      <file name="dummy.ts" path="/home/<USER>/Apps/kuroibara/frontend/app/src/dummy.ts">
        <metrics statements="1" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
      </file>
      <file name="main.js" path="/home/<USER>/Apps/kuroibara/frontend/app/src/main.js">
        <metrics statements="27" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components">
      <metrics statements="1320" coveredstatements="1297" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="BackupManager.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/components/BackupManager.vue">
        <metrics statements="417" coveredstatements="417" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="234" count="1" type="stmt"/>
        <line num="235" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="237" count="1" type="stmt"/>
        <line num="238" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="241" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="244" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="248" count="1" type="stmt"/>
        <line num="249" count="1" type="stmt"/>
        <line num="250" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="252" count="1" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="255" count="1" type="stmt"/>
        <line num="256" count="1" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="259" count="1" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="262" count="1" type="stmt"/>
        <line num="264" count="1" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="267" count="1" type="stmt"/>
        <line num="268" count="1" type="stmt"/>
        <line num="270" count="1" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="272" count="1" type="stmt"/>
        <line num="273" count="1" type="stmt"/>
        <line num="274" count="1" type="stmt"/>
        <line num="275" count="1" type="stmt"/>
        <line num="276" count="1" type="stmt"/>
        <line num="280" count="1" type="stmt"/>
        <line num="281" count="1" type="stmt"/>
        <line num="283" count="1" type="stmt"/>
        <line num="284" count="1" type="stmt"/>
        <line num="285" count="1" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="288" count="1" type="stmt"/>
        <line num="289" count="1" type="stmt"/>
        <line num="290" count="1" type="stmt"/>
        <line num="291" count="1" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
        <line num="293" count="1" type="stmt"/>
        <line num="294" count="1" type="stmt"/>
        <line num="296" count="1" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
        <line num="298" count="1" type="stmt"/>
        <line num="299" count="1" type="stmt"/>
        <line num="300" count="1" type="stmt"/>
        <line num="301" count="1" type="stmt"/>
        <line num="302" count="1" type="stmt"/>
        <line num="303" count="1" type="stmt"/>
        <line num="304" count="1" type="stmt"/>
        <line num="305" count="1" type="stmt"/>
        <line num="306" count="1" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="308" count="1" type="stmt"/>
        <line num="309" count="1" type="stmt"/>
        <line num="310" count="1" type="stmt"/>
        <line num="312" count="1" type="stmt"/>
        <line num="313" count="1" type="stmt"/>
        <line num="314" count="1" type="stmt"/>
        <line num="315" count="1" type="stmt"/>
        <line num="316" count="1" type="stmt"/>
        <line num="318" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
        <line num="320" count="1" type="stmt"/>
        <line num="321" count="1" type="stmt"/>
        <line num="322" count="1" type="stmt"/>
        <line num="323" count="1" type="stmt"/>
        <line num="324" count="1" type="stmt"/>
        <line num="325" count="1" type="stmt"/>
        <line num="326" count="1" type="stmt"/>
        <line num="327" count="1" type="stmt"/>
        <line num="328" count="1" type="stmt"/>
        <line num="329" count="1" type="stmt"/>
        <line num="330" count="1" type="stmt"/>
        <line num="331" count="1" type="stmt"/>
        <line num="332" count="1" type="stmt"/>
        <line num="333" count="1" type="stmt"/>
        <line num="334" count="1" type="stmt"/>
        <line num="335" count="1" type="stmt"/>
        <line num="336" count="1" type="stmt"/>
        <line num="337" count="1" type="stmt"/>
        <line num="338" count="1" type="stmt"/>
        <line num="339" count="1" type="stmt"/>
        <line num="340" count="1" type="stmt"/>
        <line num="341" count="1" type="stmt"/>
        <line num="342" count="1" type="stmt"/>
        <line num="344" count="1" type="stmt"/>
        <line num="345" count="1" type="stmt"/>
        <line num="346" count="1" type="stmt"/>
        <line num="347" count="1" type="stmt"/>
        <line num="348" count="1" type="stmt"/>
        <line num="349" count="1" type="stmt"/>
        <line num="350" count="1" type="stmt"/>
        <line num="351" count="1" type="stmt"/>
        <line num="352" count="1" type="stmt"/>
        <line num="353" count="1" type="stmt"/>
        <line num="354" count="1" type="stmt"/>
        <line num="355" count="1" type="stmt"/>
        <line num="356" count="1" type="stmt"/>
        <line num="357" count="1" type="stmt"/>
        <line num="358" count="1" type="stmt"/>
        <line num="359" count="1" type="stmt"/>
        <line num="360" count="1" type="stmt"/>
        <line num="361" count="1" type="stmt"/>
        <line num="362" count="1" type="stmt"/>
        <line num="363" count="1" type="stmt"/>
        <line num="364" count="1" type="stmt"/>
        <line num="365" count="1" type="stmt"/>
        <line num="366" count="1" type="stmt"/>
        <line num="368" count="1" type="stmt"/>
        <line num="369" count="1" type="stmt"/>
        <line num="370" count="1" type="stmt"/>
        <line num="371" count="1" type="stmt"/>
        <line num="372" count="1" type="stmt"/>
        <line num="373" count="1" type="stmt"/>
        <line num="374" count="1" type="stmt"/>
        <line num="375" count="1" type="stmt"/>
        <line num="376" count="1" type="stmt"/>
        <line num="377" count="1" type="stmt"/>
        <line num="378" count="1" type="stmt"/>
        <line num="379" count="1" type="stmt"/>
        <line num="380" count="1" type="stmt"/>
        <line num="381" count="1" type="stmt"/>
        <line num="382" count="1" type="stmt"/>
        <line num="384" count="1" type="stmt"/>
        <line num="385" count="1" type="stmt"/>
        <line num="386" count="1" type="stmt"/>
        <line num="387" count="1" type="stmt"/>
        <line num="388" count="1" type="stmt"/>
        <line num="389" count="1" type="stmt"/>
        <line num="390" count="1" type="stmt"/>
        <line num="391" count="1" type="stmt"/>
        <line num="392" count="1" type="stmt"/>
        <line num="393" count="1" type="stmt"/>
        <line num="394" count="1" type="stmt"/>
        <line num="395" count="1" type="stmt"/>
        <line num="396" count="1" type="stmt"/>
        <line num="397" count="1" type="stmt"/>
        <line num="398" count="1" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="400" count="1" type="stmt"/>
        <line num="401" count="1" type="stmt"/>
        <line num="402" count="1" type="stmt"/>
        <line num="403" count="1" type="stmt"/>
        <line num="404" count="1" type="stmt"/>
        <line num="405" count="1" type="stmt"/>
        <line num="406" count="1" type="stmt"/>
        <line num="407" count="1" type="stmt"/>
        <line num="408" count="1" type="stmt"/>
        <line num="409" count="1" type="stmt"/>
        <line num="410" count="1" type="stmt"/>
        <line num="411" count="1" type="stmt"/>
        <line num="412" count="1" type="stmt"/>
        <line num="413" count="1" type="stmt"/>
        <line num="414" count="1" type="stmt"/>
        <line num="415" count="1" type="stmt"/>
        <line num="416" count="1" type="stmt"/>
        <line num="417" count="1" type="stmt"/>
        <line num="418" count="1" type="stmt"/>
        <line num="419" count="1" type="stmt"/>
        <line num="420" count="1" type="stmt"/>
        <line num="422" count="1" type="stmt"/>
        <line num="423" count="1" type="stmt"/>
        <line num="424" count="1" type="stmt"/>
        <line num="425" count="1" type="stmt"/>
        <line num="426" count="1" type="stmt"/>
        <line num="427" count="1" type="stmt"/>
        <line num="428" count="1" type="stmt"/>
        <line num="429" count="1" type="stmt"/>
        <line num="431" count="1" type="stmt"/>
        <line num="432" count="1" type="stmt"/>
        <line num="433" count="1" type="stmt"/>
        <line num="434" count="1" type="stmt"/>
        <line num="436" count="1" type="stmt"/>
        <line num="437" count="1" type="stmt"/>
        <line num="438" count="1" type="stmt"/>
        <line num="440" count="1" type="stmt"/>
        <line num="441" count="1" type="stmt"/>
        <line num="443" count="1" type="stmt"/>
        <line num="444" count="1" type="stmt"/>
        <line num="445" count="1" type="stmt"/>
        <line num="446" count="1" type="stmt"/>
        <line num="448" count="1" type="stmt"/>
        <line num="449" count="1" type="stmt"/>
        <line num="450" count="1" type="stmt"/>
        <line num="451" count="1" type="stmt"/>
        <line num="452" count="1" type="stmt"/>
        <line num="453" count="1" type="stmt"/>
        <line num="454" count="1" type="stmt"/>
        <line num="456" count="1" type="stmt"/>
        <line num="457" count="1" type="stmt"/>
        <line num="458" count="1" type="stmt"/>
        <line num="459" count="1" type="stmt"/>
        <line num="460" count="1" type="stmt"/>
        <line num="461" count="1" type="stmt"/>
        <line num="462" count="1" type="stmt"/>
        <line num="464" count="1" type="stmt"/>
        <line num="465" count="1" type="stmt"/>
        <line num="466" count="1" type="stmt"/>
        <line num="468" count="1" type="stmt"/>
        <line num="469" count="1" type="stmt"/>
        <line num="470" count="1" type="stmt"/>
        <line num="471" count="1" type="stmt"/>
        <line num="472" count="1" type="stmt"/>
        <line num="473" count="1" type="stmt"/>
        <line num="474" count="1" type="stmt"/>
        <line num="476" count="1" type="stmt"/>
        <line num="477" count="1" type="stmt"/>
        <line num="478" count="1" type="stmt"/>
        <line num="479" count="1" type="stmt"/>
        <line num="480" count="1" type="stmt"/>
        <line num="481" count="1" type="stmt"/>
        <line num="482" count="1" type="stmt"/>
        <line num="484" count="1" type="stmt"/>
        <line num="485" count="1" type="stmt"/>
        <line num="486" count="1" type="stmt"/>
        <line num="487" count="1" type="stmt"/>
        <line num="488" count="1" type="stmt"/>
      </file>
      <file name="HelloWorld.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/components/HelloWorld.vue">
        <metrics statements="23" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
      </file>
      <file name="MangaCard.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/components/MangaCard.vue">
        <metrics statements="156" coveredstatements="156" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
      </file>
      <file name="ProviderCard.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/components/ProviderCard.vue">
        <metrics statements="133" coveredstatements="133" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
      </file>
      <file name="ProviderPreferences.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/components/ProviderPreferences.vue">
        <metrics statements="208" coveredstatements="208" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="221" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="234" count="1" type="stmt"/>
      </file>
      <file name="SearchResultCard.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/components/SearchResultCard.vue">
        <metrics statements="131" coveredstatements="131" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
      </file>
      <file name="StorageRecovery.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/components/StorageRecovery.vue">
        <metrics statements="252" coveredstatements="252" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="221" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="234" count="1" type="stmt"/>
        <line num="235" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="237" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="241" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="244" count="1" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="248" count="1" type="stmt"/>
        <line num="249" count="1" type="stmt"/>
        <line num="250" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="252" count="1" type="stmt"/>
        <line num="253" count="1" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="255" count="1" type="stmt"/>
        <line num="256" count="1" type="stmt"/>
        <line num="257" count="1" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="259" count="1" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="262" count="1" type="stmt"/>
        <line num="263" count="1" type="stmt"/>
        <line num="264" count="1" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="267" count="1" type="stmt"/>
        <line num="268" count="1" type="stmt"/>
        <line num="269" count="1" type="stmt"/>
        <line num="270" count="1" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="273" count="1" type="stmt"/>
        <line num="274" count="1" type="stmt"/>
        <line num="275" count="1" type="stmt"/>
        <line num="277" count="1" type="stmt"/>
        <line num="278" count="1" type="stmt"/>
        <line num="279" count="1" type="stmt"/>
        <line num="280" count="1" type="stmt"/>
        <line num="281" count="1" type="stmt"/>
        <line num="282" count="1" type="stmt"/>
        <line num="283" count="1" type="stmt"/>
        <line num="285" count="1" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="288" count="1" type="stmt"/>
        <line num="289" count="1" type="stmt"/>
        <line num="290" count="1" type="stmt"/>
        <line num="291" count="1" type="stmt"/>
        <line num="293" count="1" type="stmt"/>
        <line num="294" count="1" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="296" count="1" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
        <line num="298" count="1" type="stmt"/>
        <line num="299" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.layouts">
      <metrics statements="269" coveredstatements="269" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="DefaultLayout.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/layouts/DefaultLayout.vue">
        <metrics statements="269" coveredstatements="269" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="221" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="235" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="237" count="1" type="stmt"/>
        <line num="238" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="244" count="1" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="248" count="1" type="stmt"/>
        <line num="249" count="1" type="stmt"/>
        <line num="250" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="252" count="1" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="255" count="1" type="stmt"/>
        <line num="256" count="1" type="stmt"/>
        <line num="257" count="1" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="259" count="1" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="267" count="1" type="stmt"/>
        <line num="268" count="1" type="stmt"/>
        <line num="270" count="1" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="272" count="1" type="stmt"/>
        <line num="274" count="1" type="stmt"/>
        <line num="275" count="1" type="stmt"/>
        <line num="277" count="1" type="stmt"/>
        <line num="278" count="1" type="stmt"/>
        <line num="280" count="1" type="stmt"/>
        <line num="281" count="1" type="stmt"/>
        <line num="282" count="1" type="stmt"/>
        <line num="283" count="1" type="stmt"/>
        <line num="285" count="1" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="288" count="1" type="stmt"/>
        <line num="289" count="1" type="stmt"/>
        <line num="291" count="1" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
        <line num="293" count="1" type="stmt"/>
        <line num="294" count="1" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="296" count="1" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
        <line num="299" count="1" type="stmt"/>
        <line num="300" count="1" type="stmt"/>
        <line num="302" count="1" type="stmt"/>
        <line num="303" count="1" type="stmt"/>
        <line num="304" count="1" type="stmt"/>
        <line num="305" count="1" type="stmt"/>
        <line num="306" count="1" type="stmt"/>
        <line num="308" count="1" type="stmt"/>
        <line num="309" count="1" type="stmt"/>
        <line num="310" count="1" type="stmt"/>
        <line num="312" count="1" type="stmt"/>
        <line num="313" count="1" type="stmt"/>
        <line num="314" count="1" type="stmt"/>
        <line num="315" count="1" type="stmt"/>
        <line num="316" count="1" type="stmt"/>
        <line num="317" count="1" type="stmt"/>
        <line num="318" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.router">
      <metrics statements="141" coveredstatements="134" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="index.js" path="/home/<USER>/Apps/kuroibara/frontend/app/src/router/index.js">
        <metrics statements="141" coveredstatements="134" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.services">
      <metrics statements="44" coveredstatements="27" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="api.js" path="/home/<USER>/Apps/kuroibara/frontend/app/src/services/api.js">
        <metrics statements="44" coveredstatements="27" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.stores">
      <metrics statements="957" coveredstatements="184" conditionals="12" coveredconditionals="5" methods="71" coveredmethods="5"/>
      <file name="auth.js" path="/home/<USER>/Apps/kuroibara/frontend/app/src/stores/auth.js">
        <metrics statements="159" coveredstatements="31" conditionals="3" coveredconditionals="2" methods="8" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="171" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="172" count="2" type="stmt"/>
        <line num="173" count="2" type="stmt"/>
        <line num="175" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="2" type="stmt"/>
        <line num="180" count="2" type="stmt"/>
        <line num="181" count="2" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
      </file>
      <file name="index.js" path="/home/<USER>/Apps/kuroibara/frontend/app/src/stores/index.js">
        <metrics statements="5" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
      </file>
      <file name="library.js" path="/home/<USER>/Apps/kuroibara/frontend/app/src/stores/library.js">
        <metrics statements="112" coveredstatements="25" conditionals="0" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
      </file>
      <file name="providerPreferences.js" path="/home/<USER>/Apps/kuroibara/frontend/app/src/stores/providerPreferences.js">
        <metrics statements="154" coveredstatements="37" conditionals="0" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
      </file>
      <file name="reader.js" path="/home/<USER>/Apps/kuroibara/frontend/app/src/stores/reader.js">
        <metrics statements="151" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
      </file>
      <file name="readingLists.js" path="/home/<USER>/Apps/kuroibara/frontend/app/src/stores/readingLists.js">
        <metrics statements="126" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
      </file>
      <file name="search.js" path="/home/<USER>/Apps/kuroibara/frontend/app/src/stores/search.js">
        <metrics statements="82" coveredstatements="26" conditionals="0" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
      </file>
      <file name="settings.js" path="/home/<USER>/Apps/kuroibara/frontend/app/src/stores/settings.js">
        <metrics statements="168" coveredstatements="65" conditionals="6" coveredconditionals="3" methods="21" coveredmethods="3"/>
        <line num="1" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="172" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="173" count="2" type="stmt"/>
        <line num="174" count="2" type="stmt"/>
        <line num="175" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="176" count="2" type="stmt"/>
        <line num="178" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="2" type="stmt"/>
        <line num="181" count="2" type="stmt"/>
        <line num="182" count="2" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="185" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="186" count="2" type="stmt"/>
        <line num="188" count="2" type="stmt"/>
        <line num="189" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.views">
      <metrics statements="2673" coveredstatements="2673" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Backup.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/Backup.vue">
        <metrics statements="146" coveredstatements="146" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="221" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="235" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="237" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="241" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
      </file>
      <file name="Categories.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/Categories.vue">
        <metrics statements="360" coveredstatements="360" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="221" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="234" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="237" count="1" type="stmt"/>
        <line num="238" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="244" count="1" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="249" count="1" type="stmt"/>
        <line num="250" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="252" count="1" type="stmt"/>
        <line num="253" count="1" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="255" count="1" type="stmt"/>
        <line num="256" count="1" type="stmt"/>
        <line num="257" count="1" type="stmt"/>
        <line num="259" count="1" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="262" count="1" type="stmt"/>
        <line num="263" count="1" type="stmt"/>
        <line num="264" count="1" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="267" count="1" type="stmt"/>
        <line num="268" count="1" type="stmt"/>
        <line num="269" count="1" type="stmt"/>
        <line num="270" count="1" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="272" count="1" type="stmt"/>
        <line num="274" count="1" type="stmt"/>
        <line num="275" count="1" type="stmt"/>
        <line num="276" count="1" type="stmt"/>
        <line num="277" count="1" type="stmt"/>
        <line num="279" count="1" type="stmt"/>
        <line num="280" count="1" type="stmt"/>
        <line num="281" count="1" type="stmt"/>
        <line num="282" count="1" type="stmt"/>
        <line num="283" count="1" type="stmt"/>
        <line num="284" count="1" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="288" count="1" type="stmt"/>
        <line num="289" count="1" type="stmt"/>
        <line num="290" count="1" type="stmt"/>
        <line num="291" count="1" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="296" count="1" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
        <line num="299" count="1" type="stmt"/>
        <line num="301" count="1" type="stmt"/>
        <line num="302" count="1" type="stmt"/>
        <line num="303" count="1" type="stmt"/>
        <line num="305" count="1" type="stmt"/>
        <line num="306" count="1" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="308" count="1" type="stmt"/>
        <line num="309" count="1" type="stmt"/>
        <line num="310" count="1" type="stmt"/>
        <line num="312" count="1" type="stmt"/>
        <line num="313" count="1" type="stmt"/>
        <line num="314" count="1" type="stmt"/>
        <line num="315" count="1" type="stmt"/>
        <line num="316" count="1" type="stmt"/>
        <line num="317" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
        <line num="320" count="1" type="stmt"/>
        <line num="321" count="1" type="stmt"/>
        <line num="323" count="1" type="stmt"/>
        <line num="324" count="1" type="stmt"/>
        <line num="325" count="1" type="stmt"/>
        <line num="326" count="1" type="stmt"/>
        <line num="327" count="1" type="stmt"/>
        <line num="328" count="1" type="stmt"/>
        <line num="329" count="1" type="stmt"/>
        <line num="330" count="1" type="stmt"/>
        <line num="331" count="1" type="stmt"/>
        <line num="332" count="1" type="stmt"/>
        <line num="334" count="1" type="stmt"/>
        <line num="335" count="1" type="stmt"/>
        <line num="336" count="1" type="stmt"/>
        <line num="338" count="1" type="stmt"/>
        <line num="339" count="1" type="stmt"/>
        <line num="340" count="1" type="stmt"/>
        <line num="341" count="1" type="stmt"/>
        <line num="342" count="1" type="stmt"/>
        <line num="343" count="1" type="stmt"/>
        <line num="344" count="1" type="stmt"/>
        <line num="345" count="1" type="stmt"/>
        <line num="346" count="1" type="stmt"/>
        <line num="347" count="1" type="stmt"/>
        <line num="349" count="1" type="stmt"/>
        <line num="350" count="1" type="stmt"/>
        <line num="352" count="1" type="stmt"/>
        <line num="353" count="1" type="stmt"/>
        <line num="354" count="1" type="stmt"/>
        <line num="356" count="1" type="stmt"/>
        <line num="357" count="1" type="stmt"/>
        <line num="359" count="1" type="stmt"/>
        <line num="361" count="1" type="stmt"/>
        <line num="362" count="1" type="stmt"/>
        <line num="363" count="1" type="stmt"/>
        <line num="364" count="1" type="stmt"/>
        <line num="365" count="1" type="stmt"/>
        <line num="366" count="1" type="stmt"/>
        <line num="367" count="1" type="stmt"/>
        <line num="368" count="1" type="stmt"/>
        <line num="370" count="1" type="stmt"/>
        <line num="371" count="1" type="stmt"/>
        <line num="373" count="1" type="stmt"/>
        <line num="374" count="1" type="stmt"/>
        <line num="375" count="1" type="stmt"/>
        <line num="376" count="1" type="stmt"/>
        <line num="377" count="1" type="stmt"/>
        <line num="378" count="1" type="stmt"/>
        <line num="379" count="1" type="stmt"/>
        <line num="380" count="1" type="stmt"/>
        <line num="381" count="1" type="stmt"/>
        <line num="383" count="1" type="stmt"/>
        <line num="384" count="1" type="stmt"/>
        <line num="386" count="1" type="stmt"/>
        <line num="388" count="1" type="stmt"/>
        <line num="389" count="1" type="stmt"/>
        <line num="391" count="1" type="stmt"/>
        <line num="392" count="1" type="stmt"/>
        <line num="394" count="1" type="stmt"/>
        <line num="395" count="1" type="stmt"/>
        <line num="396" count="1" type="stmt"/>
        <line num="397" count="1" type="stmt"/>
        <line num="398" count="1" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="400" count="1" type="stmt"/>
        <line num="401" count="1" type="stmt"/>
        <line num="402" count="1" type="stmt"/>
        <line num="403" count="1" type="stmt"/>
        <line num="405" count="1" type="stmt"/>
        <line num="406" count="1" type="stmt"/>
        <line num="407" count="1" type="stmt"/>
        <line num="409" count="1" type="stmt"/>
        <line num="410" count="1" type="stmt"/>
        <line num="411" count="1" type="stmt"/>
        <line num="412" count="1" type="stmt"/>
        <line num="413" count="1" type="stmt"/>
        <line num="414" count="1" type="stmt"/>
        <line num="415" count="1" type="stmt"/>
        <line num="417" count="1" type="stmt"/>
        <line num="418" count="1" type="stmt"/>
        <line num="420" count="1" type="stmt"/>
        <line num="421" count="1" type="stmt"/>
        <line num="422" count="1" type="stmt"/>
      </file>
      <file name="Home.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/Home.vue">
        <metrics statements="114" coveredstatements="114" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
      </file>
      <file name="Library.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/Library.vue">
        <metrics statements="262" coveredstatements="262" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="221" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="235" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="238" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="244" count="1" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="248" count="1" type="stmt"/>
        <line num="249" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="252" count="1" type="stmt"/>
        <line num="253" count="1" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="255" count="1" type="stmt"/>
        <line num="257" count="1" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="259" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="262" count="1" type="stmt"/>
        <line num="264" count="1" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="267" count="1" type="stmt"/>
        <line num="269" count="1" type="stmt"/>
        <line num="270" count="1" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="272" count="1" type="stmt"/>
        <line num="273" count="1" type="stmt"/>
        <line num="274" count="1" type="stmt"/>
        <line num="275" count="1" type="stmt"/>
        <line num="276" count="1" type="stmt"/>
        <line num="277" count="1" type="stmt"/>
        <line num="279" count="1" type="stmt"/>
        <line num="280" count="1" type="stmt"/>
        <line num="281" count="1" type="stmt"/>
        <line num="283" count="1" type="stmt"/>
        <line num="284" count="1" type="stmt"/>
        <line num="285" count="1" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="288" count="1" type="stmt"/>
        <line num="289" count="1" type="stmt"/>
        <line num="290" count="1" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
        <line num="293" count="1" type="stmt"/>
        <line num="294" count="1" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="296" count="1" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
        <line num="298" count="1" type="stmt"/>
        <line num="300" count="1" type="stmt"/>
        <line num="301" count="1" type="stmt"/>
        <line num="302" count="1" type="stmt"/>
        <line num="303" count="1" type="stmt"/>
        <line num="304" count="1" type="stmt"/>
        <line num="306" count="1" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="308" count="1" type="stmt"/>
        <line num="309" count="1" type="stmt"/>
        <line num="310" count="1" type="stmt"/>
        <line num="312" count="1" type="stmt"/>
        <line num="313" count="1" type="stmt"/>
        <line num="314" count="1" type="stmt"/>
        <line num="316" count="1" type="stmt"/>
        <line num="317" count="1" type="stmt"/>
        <line num="318" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
        <line num="321" count="1" type="stmt"/>
        <line num="322" count="1" type="stmt"/>
        <line num="323" count="1" type="stmt"/>
        <line num="324" count="1" type="stmt"/>
        <line num="325" count="1" type="stmt"/>
      </file>
      <file name="MangaDetails.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/MangaDetails.vue">
        <metrics statements="365" coveredstatements="365" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="234" count="1" type="stmt"/>
        <line num="235" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="237" count="1" type="stmt"/>
        <line num="238" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="244" count="1" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="248" count="1" type="stmt"/>
        <line num="250" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="252" count="1" type="stmt"/>
        <line num="253" count="1" type="stmt"/>
        <line num="255" count="1" type="stmt"/>
        <line num="256" count="1" type="stmt"/>
        <line num="257" count="1" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="259" count="1" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="262" count="1" type="stmt"/>
        <line num="264" count="1" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="267" count="1" type="stmt"/>
        <line num="268" count="1" type="stmt"/>
        <line num="270" count="1" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="272" count="1" type="stmt"/>
        <line num="274" count="1" type="stmt"/>
        <line num="275" count="1" type="stmt"/>
        <line num="276" count="1" type="stmt"/>
        <line num="277" count="1" type="stmt"/>
        <line num="278" count="1" type="stmt"/>
        <line num="279" count="1" type="stmt"/>
        <line num="280" count="1" type="stmt"/>
        <line num="282" count="1" type="stmt"/>
        <line num="283" count="1" type="stmt"/>
        <line num="284" count="1" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="288" count="1" type="stmt"/>
        <line num="290" count="1" type="stmt"/>
        <line num="291" count="1" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
        <line num="293" count="1" type="stmt"/>
        <line num="294" count="1" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="296" count="1" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
        <line num="298" count="1" type="stmt"/>
        <line num="300" count="1" type="stmt"/>
        <line num="302" count="1" type="stmt"/>
        <line num="303" count="1" type="stmt"/>
        <line num="304" count="1" type="stmt"/>
        <line num="305" count="1" type="stmt"/>
        <line num="306" count="1" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="308" count="1" type="stmt"/>
        <line num="309" count="1" type="stmt"/>
        <line num="310" count="1" type="stmt"/>
        <line num="311" count="1" type="stmt"/>
        <line num="312" count="1" type="stmt"/>
        <line num="313" count="1" type="stmt"/>
        <line num="314" count="1" type="stmt"/>
        <line num="315" count="1" type="stmt"/>
        <line num="316" count="1" type="stmt"/>
        <line num="318" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
        <line num="320" count="1" type="stmt"/>
        <line num="321" count="1" type="stmt"/>
        <line num="322" count="1" type="stmt"/>
        <line num="323" count="1" type="stmt"/>
        <line num="324" count="1" type="stmt"/>
        <line num="325" count="1" type="stmt"/>
        <line num="327" count="1" type="stmt"/>
        <line num="328" count="1" type="stmt"/>
        <line num="329" count="1" type="stmt"/>
        <line num="331" count="1" type="stmt"/>
        <line num="332" count="1" type="stmt"/>
        <line num="333" count="1" type="stmt"/>
        <line num="334" count="1" type="stmt"/>
        <line num="335" count="1" type="stmt"/>
        <line num="336" count="1" type="stmt"/>
        <line num="337" count="1" type="stmt"/>
        <line num="338" count="1" type="stmt"/>
        <line num="339" count="1" type="stmt"/>
        <line num="340" count="1" type="stmt"/>
        <line num="342" count="1" type="stmt"/>
        <line num="343" count="1" type="stmt"/>
        <line num="344" count="1" type="stmt"/>
        <line num="345" count="1" type="stmt"/>
        <line num="346" count="1" type="stmt"/>
        <line num="347" count="1" type="stmt"/>
        <line num="348" count="1" type="stmt"/>
        <line num="350" count="1" type="stmt"/>
        <line num="351" count="1" type="stmt"/>
        <line num="352" count="1" type="stmt"/>
        <line num="353" count="1" type="stmt"/>
        <line num="354" count="1" type="stmt"/>
        <line num="355" count="1" type="stmt"/>
        <line num="356" count="1" type="stmt"/>
        <line num="357" count="1" type="stmt"/>
        <line num="358" count="1" type="stmt"/>
        <line num="359" count="1" type="stmt"/>
        <line num="361" count="1" type="stmt"/>
        <line num="362" count="1" type="stmt"/>
        <line num="363" count="1" type="stmt"/>
        <line num="364" count="1" type="stmt"/>
        <line num="365" count="1" type="stmt"/>
        <line num="366" count="1" type="stmt"/>
        <line num="368" count="1" type="stmt"/>
        <line num="369" count="1" type="stmt"/>
        <line num="370" count="1" type="stmt"/>
        <line num="372" count="1" type="stmt"/>
        <line num="373" count="1" type="stmt"/>
        <line num="374" count="1" type="stmt"/>
        <line num="375" count="1" type="stmt"/>
        <line num="376" count="1" type="stmt"/>
        <line num="377" count="1" type="stmt"/>
        <line num="379" count="1" type="stmt"/>
        <line num="380" count="1" type="stmt"/>
        <line num="381" count="1" type="stmt"/>
        <line num="383" count="1" type="stmt"/>
        <line num="384" count="1" type="stmt"/>
        <line num="385" count="1" type="stmt"/>
        <line num="387" count="1" type="stmt"/>
        <line num="388" count="1" type="stmt"/>
        <line num="389" count="1" type="stmt"/>
        <line num="390" count="1" type="stmt"/>
        <line num="391" count="1" type="stmt"/>
        <line num="392" count="1" type="stmt"/>
        <line num="393" count="1" type="stmt"/>
        <line num="395" count="1" type="stmt"/>
        <line num="396" count="1" type="stmt"/>
        <line num="397" count="1" type="stmt"/>
        <line num="398" count="1" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="400" count="1" type="stmt"/>
        <line num="402" count="1" type="stmt"/>
        <line num="403" count="1" type="stmt"/>
        <line num="404" count="1" type="stmt"/>
        <line num="405" count="1" type="stmt"/>
        <line num="406" count="1" type="stmt"/>
        <line num="407" count="1" type="stmt"/>
        <line num="408" count="1" type="stmt"/>
        <line num="409" count="1" type="stmt"/>
        <line num="410" count="1" type="stmt"/>
        <line num="412" count="1" type="stmt"/>
        <line num="413" count="1" type="stmt"/>
        <line num="415" count="1" type="stmt"/>
        <line num="416" count="1" type="stmt"/>
        <line num="417" count="1" type="stmt"/>
        <line num="418" count="1" type="stmt"/>
        <line num="419" count="1" type="stmt"/>
        <line num="420" count="1" type="stmt"/>
        <line num="421" count="1" type="stmt"/>
        <line num="423" count="1" type="stmt"/>
        <line num="424" count="1" type="stmt"/>
        <line num="426" count="1" type="stmt"/>
        <line num="427" count="1" type="stmt"/>
        <line num="428" count="1" type="stmt"/>
        <line num="430" count="1" type="stmt"/>
        <line num="431" count="1" type="stmt"/>
        <line num="432" count="1" type="stmt"/>
      </file>
      <file name="MangaReader.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/MangaReader.vue">
        <metrics statements="261" coveredstatements="261" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="221" count="1" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="235" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="237" count="1" type="stmt"/>
        <line num="238" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="241" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="244" count="1" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="249" count="1" type="stmt"/>
        <line num="250" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="252" count="1" type="stmt"/>
        <line num="253" count="1" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="255" count="1" type="stmt"/>
        <line num="256" count="1" type="stmt"/>
        <line num="257" count="1" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="259" count="1" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="263" count="1" type="stmt"/>
        <line num="264" count="1" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="268" count="1" type="stmt"/>
        <line num="269" count="1" type="stmt"/>
        <line num="270" count="1" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="278" count="1" type="stmt"/>
        <line num="279" count="1" type="stmt"/>
        <line num="280" count="1" type="stmt"/>
        <line num="281" count="1" type="stmt"/>
        <line num="282" count="1" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="288" count="1" type="stmt"/>
        <line num="289" count="1" type="stmt"/>
        <line num="290" count="1" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
        <line num="293" count="1" type="stmt"/>
        <line num="294" count="1" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
        <line num="298" count="1" type="stmt"/>
        <line num="299" count="1" type="stmt"/>
        <line num="300" count="1" type="stmt"/>
        <line num="301" count="1" type="stmt"/>
        <line num="302" count="1" type="stmt"/>
        <line num="303" count="1" type="stmt"/>
        <line num="304" count="1" type="stmt"/>
        <line num="305" count="1" type="stmt"/>
        <line num="306" count="1" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="309" count="1" type="stmt"/>
        <line num="310" count="1" type="stmt"/>
        <line num="311" count="1" type="stmt"/>
        <line num="312" count="1" type="stmt"/>
        <line num="313" count="1" type="stmt"/>
        <line num="314" count="1" type="stmt"/>
        <line num="315" count="1" type="stmt"/>
        <line num="316" count="1" type="stmt"/>
        <line num="317" count="1" type="stmt"/>
        <line num="318" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
      </file>
      <file name="NotFound.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/NotFound.vue">
        <metrics statements="26" coveredstatements="26" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
      </file>
      <file name="ReadingLists.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/ReadingLists.vue">
        <metrics statements="344" coveredstatements="344" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="221" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="234" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="237" count="1" type="stmt"/>
        <line num="238" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="241" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="244" count="1" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="248" count="1" type="stmt"/>
        <line num="249" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="252" count="1" type="stmt"/>
        <line num="253" count="1" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="256" count="1" type="stmt"/>
        <line num="257" count="1" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="259" count="1" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="263" count="1" type="stmt"/>
        <line num="264" count="1" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="267" count="1" type="stmt"/>
        <line num="268" count="1" type="stmt"/>
        <line num="272" count="1" type="stmt"/>
        <line num="273" count="1" type="stmt"/>
        <line num="274" count="1" type="stmt"/>
        <line num="276" count="1" type="stmt"/>
        <line num="278" count="1" type="stmt"/>
        <line num="279" count="1" type="stmt"/>
        <line num="280" count="1" type="stmt"/>
        <line num="282" count="1" type="stmt"/>
        <line num="283" count="1" type="stmt"/>
        <line num="284" count="1" type="stmt"/>
        <line num="285" count="1" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="289" count="1" type="stmt"/>
        <line num="290" count="1" type="stmt"/>
        <line num="291" count="1" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
        <line num="293" count="1" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="296" count="1" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
        <line num="299" count="1" type="stmt"/>
        <line num="300" count="1" type="stmt"/>
        <line num="301" count="1" type="stmt"/>
        <line num="302" count="1" type="stmt"/>
        <line num="303" count="1" type="stmt"/>
        <line num="304" count="1" type="stmt"/>
        <line num="305" count="1" type="stmt"/>
        <line num="306" count="1" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="308" count="1" type="stmt"/>
        <line num="310" count="1" type="stmt"/>
        <line num="311" count="1" type="stmt"/>
        <line num="312" count="1" type="stmt"/>
        <line num="314" count="1" type="stmt"/>
        <line num="315" count="1" type="stmt"/>
        <line num="316" count="1" type="stmt"/>
        <line num="317" count="1" type="stmt"/>
        <line num="318" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
        <line num="320" count="1" type="stmt"/>
        <line num="321" count="1" type="stmt"/>
        <line num="322" count="1" type="stmt"/>
        <line num="324" count="1" type="stmt"/>
        <line num="325" count="1" type="stmt"/>
        <line num="326" count="1" type="stmt"/>
        <line num="327" count="1" type="stmt"/>
        <line num="329" count="1" type="stmt"/>
        <line num="330" count="1" type="stmt"/>
        <line num="332" count="1" type="stmt"/>
        <line num="333" count="1" type="stmt"/>
        <line num="335" count="1" type="stmt"/>
        <line num="336" count="1" type="stmt"/>
        <line num="337" count="1" type="stmt"/>
        <line num="338" count="1" type="stmt"/>
        <line num="339" count="1" type="stmt"/>
        <line num="340" count="1" type="stmt"/>
        <line num="341" count="1" type="stmt"/>
        <line num="342" count="1" type="stmt"/>
        <line num="343" count="1" type="stmt"/>
        <line num="344" count="1" type="stmt"/>
        <line num="345" count="1" type="stmt"/>
        <line num="346" count="1" type="stmt"/>
        <line num="347" count="1" type="stmt"/>
        <line num="348" count="1" type="stmt"/>
        <line num="350" count="1" type="stmt"/>
        <line num="351" count="1" type="stmt"/>
        <line num="353" count="1" type="stmt"/>
        <line num="354" count="1" type="stmt"/>
        <line num="355" count="1" type="stmt"/>
        <line num="356" count="1" type="stmt"/>
        <line num="357" count="1" type="stmt"/>
        <line num="358" count="1" type="stmt"/>
        <line num="359" count="1" type="stmt"/>
        <line num="360" count="1" type="stmt"/>
        <line num="361" count="1" type="stmt"/>
        <line num="363" count="1" type="stmt"/>
        <line num="364" count="1" type="stmt"/>
        <line num="366" count="1" type="stmt"/>
        <line num="368" count="1" type="stmt"/>
        <line num="369" count="1" type="stmt"/>
        <line num="371" count="1" type="stmt"/>
        <line num="372" count="1" type="stmt"/>
        <line num="374" count="1" type="stmt"/>
        <line num="375" count="1" type="stmt"/>
        <line num="376" count="1" type="stmt"/>
        <line num="377" count="1" type="stmt"/>
        <line num="378" count="1" type="stmt"/>
        <line num="379" count="1" type="stmt"/>
        <line num="380" count="1" type="stmt"/>
        <line num="381" count="1" type="stmt"/>
        <line num="382" count="1" type="stmt"/>
        <line num="383" count="1" type="stmt"/>
        <line num="385" count="1" type="stmt"/>
        <line num="386" count="1" type="stmt"/>
        <line num="387" count="1" type="stmt"/>
        <line num="389" count="1" type="stmt"/>
        <line num="390" count="1" type="stmt"/>
        <line num="391" count="1" type="stmt"/>
        <line num="392" count="1" type="stmt"/>
        <line num="393" count="1" type="stmt"/>
        <line num="394" count="1" type="stmt"/>
        <line num="396" count="1" type="stmt"/>
        <line num="397" count="1" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="400" count="1" type="stmt"/>
        <line num="401" count="1" type="stmt"/>
      </file>
      <file name="Recovery.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/Recovery.vue">
        <metrics statements="50" coveredstatements="50" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
      </file>
      <file name="Search.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/Search.vue">
        <metrics statements="368" coveredstatements="368" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="237" count="1" type="stmt"/>
        <line num="238" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="241" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="244" count="1" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="249" count="1" type="stmt"/>
        <line num="250" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="252" count="1" type="stmt"/>
        <line num="253" count="1" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="256" count="1" type="stmt"/>
        <line num="257" count="1" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="259" count="1" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="263" count="1" type="stmt"/>
        <line num="264" count="1" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="267" count="1" type="stmt"/>
        <line num="268" count="1" type="stmt"/>
        <line num="270" count="1" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="273" count="1" type="stmt"/>
        <line num="274" count="1" type="stmt"/>
        <line num="275" count="1" type="stmt"/>
        <line num="277" count="1" type="stmt"/>
        <line num="278" count="1" type="stmt"/>
        <line num="279" count="1" type="stmt"/>
        <line num="281" count="1" type="stmt"/>
        <line num="282" count="1" type="stmt"/>
        <line num="284" count="1" type="stmt"/>
        <line num="285" count="1" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="288" count="1" type="stmt"/>
        <line num="289" count="1" type="stmt"/>
        <line num="290" count="1" type="stmt"/>
        <line num="291" count="1" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
        <line num="293" count="1" type="stmt"/>
        <line num="294" count="1" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="296" count="1" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
        <line num="298" count="1" type="stmt"/>
        <line num="299" count="1" type="stmt"/>
        <line num="300" count="1" type="stmt"/>
        <line num="301" count="1" type="stmt"/>
        <line num="303" count="1" type="stmt"/>
        <line num="304" count="1" type="stmt"/>
        <line num="305" count="1" type="stmt"/>
        <line num="306" count="1" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="309" count="1" type="stmt"/>
        <line num="310" count="1" type="stmt"/>
        <line num="311" count="1" type="stmt"/>
        <line num="312" count="1" type="stmt"/>
        <line num="313" count="1" type="stmt"/>
        <line num="315" count="1" type="stmt"/>
        <line num="316" count="1" type="stmt"/>
        <line num="317" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
        <line num="320" count="1" type="stmt"/>
        <line num="321" count="1" type="stmt"/>
        <line num="322" count="1" type="stmt"/>
        <line num="323" count="1" type="stmt"/>
        <line num="324" count="1" type="stmt"/>
        <line num="325" count="1" type="stmt"/>
        <line num="326" count="1" type="stmt"/>
        <line num="327" count="1" type="stmt"/>
        <line num="329" count="1" type="stmt"/>
        <line num="330" count="1" type="stmt"/>
        <line num="331" count="1" type="stmt"/>
        <line num="332" count="1" type="stmt"/>
        <line num="333" count="1" type="stmt"/>
        <line num="334" count="1" type="stmt"/>
        <line num="335" count="1" type="stmt"/>
        <line num="336" count="1" type="stmt"/>
        <line num="337" count="1" type="stmt"/>
        <line num="339" count="1" type="stmt"/>
        <line num="340" count="1" type="stmt"/>
        <line num="341" count="1" type="stmt"/>
        <line num="343" count="1" type="stmt"/>
        <line num="344" count="1" type="stmt"/>
        <line num="345" count="1" type="stmt"/>
        <line num="346" count="1" type="stmt"/>
        <line num="347" count="1" type="stmt"/>
        <line num="348" count="1" type="stmt"/>
        <line num="350" count="1" type="stmt"/>
        <line num="351" count="1" type="stmt"/>
        <line num="352" count="1" type="stmt"/>
        <line num="353" count="1" type="stmt"/>
        <line num="354" count="1" type="stmt"/>
        <line num="355" count="1" type="stmt"/>
        <line num="356" count="1" type="stmt"/>
        <line num="358" count="1" type="stmt"/>
        <line num="359" count="1" type="stmt"/>
        <line num="361" count="1" type="stmt"/>
        <line num="362" count="1" type="stmt"/>
        <line num="363" count="1" type="stmt"/>
        <line num="365" count="1" type="stmt"/>
        <line num="366" count="1" type="stmt"/>
        <line num="367" count="1" type="stmt"/>
        <line num="368" count="1" type="stmt"/>
        <line num="369" count="1" type="stmt"/>
        <line num="371" count="1" type="stmt"/>
        <line num="372" count="1" type="stmt"/>
        <line num="373" count="1" type="stmt"/>
        <line num="375" count="1" type="stmt"/>
        <line num="376" count="1" type="stmt"/>
        <line num="377" count="1" type="stmt"/>
        <line num="379" count="1" type="stmt"/>
        <line num="380" count="1" type="stmt"/>
        <line num="381" count="1" type="stmt"/>
        <line num="382" count="1" type="stmt"/>
        <line num="383" count="1" type="stmt"/>
        <line num="384" count="1" type="stmt"/>
        <line num="386" count="1" type="stmt"/>
        <line num="387" count="1" type="stmt"/>
        <line num="388" count="1" type="stmt"/>
        <line num="389" count="1" type="stmt"/>
        <line num="390" count="1" type="stmt"/>
        <line num="392" count="1" type="stmt"/>
        <line num="393" count="1" type="stmt"/>
        <line num="394" count="1" type="stmt"/>
        <line num="396" count="1" type="stmt"/>
        <line num="397" count="1" type="stmt"/>
        <line num="398" count="1" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="400" count="1" type="stmt"/>
        <line num="401" count="1" type="stmt"/>
        <line num="402" count="1" type="stmt"/>
        <line num="403" count="1" type="stmt"/>
        <line num="404" count="1" type="stmt"/>
        <line num="406" count="1" type="stmt"/>
        <line num="408" count="1" type="stmt"/>
        <line num="409" count="1" type="stmt"/>
        <line num="410" count="1" type="stmt"/>
        <line num="411" count="1" type="stmt"/>
        <line num="412" count="1" type="stmt"/>
        <line num="413" count="1" type="stmt"/>
        <line num="414" count="1" type="stmt"/>
        <line num="415" count="1" type="stmt"/>
        <line num="416" count="1" type="stmt"/>
        <line num="417" count="1" type="stmt"/>
        <line num="419" count="1" type="stmt"/>
        <line num="420" count="1" type="stmt"/>
        <line num="421" count="1" type="stmt"/>
        <line num="422" count="1" type="stmt"/>
        <line num="423" count="1" type="stmt"/>
        <line num="424" count="1" type="stmt"/>
        <line num="426" count="1" type="stmt"/>
        <line num="427" count="1" type="stmt"/>
        <line num="428" count="1" type="stmt"/>
        <line num="430" count="1" type="stmt"/>
        <line num="431" count="1" type="stmt"/>
        <line num="432" count="1" type="stmt"/>
        <line num="433" count="1" type="stmt"/>
        <line num="435" count="1" type="stmt"/>
        <line num="436" count="1" type="stmt"/>
        <line num="437" count="1" type="stmt"/>
        <line num="438" count="1" type="stmt"/>
        <line num="440" count="1" type="stmt"/>
        <line num="441" count="1" type="stmt"/>
        <line num="442" count="1" type="stmt"/>
        <line num="444" count="1" type="stmt"/>
        <line num="445" count="1" type="stmt"/>
        <line num="446" count="1" type="stmt"/>
        <line num="447" count="1" type="stmt"/>
        <line num="449" count="1" type="stmt"/>
        <line num="450" count="1" type="stmt"/>
        <line num="451" count="1" type="stmt"/>
        <line num="453" count="1" type="stmt"/>
        <line num="454" count="1" type="stmt"/>
        <line num="455" count="1" type="stmt"/>
        <line num="456" count="1" type="stmt"/>
        <line num="457" count="1" type="stmt"/>
        <line num="458" count="1" type="stmt"/>
      </file>
      <file name="Settings.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/Settings.vue">
        <metrics statements="377" coveredstatements="377" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="221" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="235" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="238" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="241" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="244" count="1" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="248" count="1" type="stmt"/>
        <line num="249" count="1" type="stmt"/>
        <line num="250" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="253" count="1" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="255" count="1" type="stmt"/>
        <line num="256" count="1" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="259" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="262" count="1" type="stmt"/>
        <line num="263" count="1" type="stmt"/>
        <line num="264" count="1" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="268" count="1" type="stmt"/>
        <line num="269" count="1" type="stmt"/>
        <line num="270" count="1" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="272" count="1" type="stmt"/>
        <line num="273" count="1" type="stmt"/>
        <line num="275" count="1" type="stmt"/>
        <line num="276" count="1" type="stmt"/>
        <line num="277" count="1" type="stmt"/>
        <line num="279" count="1" type="stmt"/>
        <line num="280" count="1" type="stmt"/>
        <line num="282" count="1" type="stmt"/>
        <line num="283" count="1" type="stmt"/>
        <line num="284" count="1" type="stmt"/>
        <line num="285" count="1" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="289" count="1" type="stmt"/>
        <line num="290" count="1" type="stmt"/>
        <line num="291" count="1" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
        <line num="293" count="1" type="stmt"/>
        <line num="294" count="1" type="stmt"/>
        <line num="296" count="1" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
        <line num="298" count="1" type="stmt"/>
        <line num="300" count="1" type="stmt"/>
        <line num="301" count="1" type="stmt"/>
        <line num="303" count="1" type="stmt"/>
        <line num="304" count="1" type="stmt"/>
        <line num="305" count="1" type="stmt"/>
        <line num="306" count="1" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="308" count="1" type="stmt"/>
        <line num="310" count="1" type="stmt"/>
        <line num="311" count="1" type="stmt"/>
        <line num="312" count="1" type="stmt"/>
        <line num="313" count="1" type="stmt"/>
        <line num="314" count="1" type="stmt"/>
        <line num="315" count="1" type="stmt"/>
        <line num="316" count="1" type="stmt"/>
        <line num="317" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
        <line num="320" count="1" type="stmt"/>
        <line num="321" count="1" type="stmt"/>
        <line num="322" count="1" type="stmt"/>
        <line num="323" count="1" type="stmt"/>
        <line num="324" count="1" type="stmt"/>
        <line num="326" count="1" type="stmt"/>
        <line num="327" count="1" type="stmt"/>
        <line num="328" count="1" type="stmt"/>
        <line num="329" count="1" type="stmt"/>
        <line num="331" count="1" type="stmt"/>
        <line num="332" count="1" type="stmt"/>
        <line num="333" count="1" type="stmt"/>
        <line num="334" count="1" type="stmt"/>
        <line num="335" count="1" type="stmt"/>
        <line num="336" count="1" type="stmt"/>
        <line num="340" count="1" type="stmt"/>
        <line num="341" count="1" type="stmt"/>
        <line num="342" count="1" type="stmt"/>
        <line num="344" count="1" type="stmt"/>
        <line num="346" count="1" type="stmt"/>
        <line num="347" count="1" type="stmt"/>
        <line num="348" count="1" type="stmt"/>
        <line num="349" count="1" type="stmt"/>
        <line num="350" count="1" type="stmt"/>
        <line num="352" count="1" type="stmt"/>
        <line num="353" count="1" type="stmt"/>
        <line num="354" count="1" type="stmt"/>
        <line num="355" count="1" type="stmt"/>
        <line num="356" count="1" type="stmt"/>
        <line num="357" count="1" type="stmt"/>
        <line num="359" count="1" type="stmt"/>
        <line num="360" count="1" type="stmt"/>
        <line num="361" count="1" type="stmt"/>
        <line num="363" count="1" type="stmt"/>
        <line num="364" count="1" type="stmt"/>
        <line num="365" count="1" type="stmt"/>
        <line num="366" count="1" type="stmt"/>
        <line num="367" count="1" type="stmt"/>
        <line num="368" count="1" type="stmt"/>
        <line num="369" count="1" type="stmt"/>
        <line num="370" count="1" type="stmt"/>
        <line num="371" count="1" type="stmt"/>
        <line num="372" count="1" type="stmt"/>
        <line num="373" count="1" type="stmt"/>
        <line num="374" count="1" type="stmt"/>
        <line num="376" count="1" type="stmt"/>
        <line num="377" count="1" type="stmt"/>
        <line num="378" count="1" type="stmt"/>
        <line num="379" count="1" type="stmt"/>
        <line num="380" count="1" type="stmt"/>
        <line num="381" count="1" type="stmt"/>
        <line num="382" count="1" type="stmt"/>
        <line num="383" count="1" type="stmt"/>
        <line num="384" count="1" type="stmt"/>
        <line num="386" count="1" type="stmt"/>
        <line num="387" count="1" type="stmt"/>
        <line num="388" count="1" type="stmt"/>
        <line num="389" count="1" type="stmt"/>
        <line num="390" count="1" type="stmt"/>
        <line num="391" count="1" type="stmt"/>
        <line num="392" count="1" type="stmt"/>
        <line num="394" count="1" type="stmt"/>
        <line num="395" count="1" type="stmt"/>
        <line num="396" count="1" type="stmt"/>
        <line num="397" count="1" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="400" count="1" type="stmt"/>
        <line num="401" count="1" type="stmt"/>
        <line num="403" count="1" type="stmt"/>
        <line num="404" count="1" type="stmt"/>
        <line num="405" count="1" type="stmt"/>
        <line num="406" count="1" type="stmt"/>
        <line num="407" count="1" type="stmt"/>
        <line num="408" count="1" type="stmt"/>
        <line num="409" count="1" type="stmt"/>
        <line num="410" count="1" type="stmt"/>
        <line num="411" count="1" type="stmt"/>
        <line num="412" count="1" type="stmt"/>
        <line num="413" count="1" type="stmt"/>
        <line num="414" count="1" type="stmt"/>
        <line num="415" count="1" type="stmt"/>
        <line num="416" count="1" type="stmt"/>
        <line num="417" count="1" type="stmt"/>
        <line num="418" count="1" type="stmt"/>
        <line num="419" count="1" type="stmt"/>
        <line num="421" count="1" type="stmt"/>
        <line num="422" count="1" type="stmt"/>
        <line num="423" count="1" type="stmt"/>
        <line num="425" count="1" type="stmt"/>
        <line num="426" count="1" type="stmt"/>
        <line num="427" count="1" type="stmt"/>
        <line num="428" count="1" type="stmt"/>
        <line num="430" count="1" type="stmt"/>
        <line num="431" count="1" type="stmt"/>
        <line num="432" count="1" type="stmt"/>
        <line num="434" count="1" type="stmt"/>
        <line num="435" count="1" type="stmt"/>
        <line num="436" count="1" type="stmt"/>
        <line num="438" count="1" type="stmt"/>
        <line num="439" count="1" type="stmt"/>
        <line num="440" count="1" type="stmt"/>
        <line num="441" count="1" type="stmt"/>
        <line num="442" count="1" type="stmt"/>
        <line num="444" count="1" type="stmt"/>
        <line num="445" count="1" type="stmt"/>
        <line num="446" count="1" type="stmt"/>
        <line num="447" count="1" type="stmt"/>
        <line num="448" count="1" type="stmt"/>
        <line num="449" count="1" type="stmt"/>
        <line num="450" count="1" type="stmt"/>
        <line num="451" count="1" type="stmt"/>
        <line num="453" count="1" type="stmt"/>
        <line num="454" count="1" type="stmt"/>
        <line num="456" count="1" type="stmt"/>
        <line num="457" count="1" type="stmt"/>
        <line num="458" count="1" type="stmt"/>
        <line num="459" count="1" type="stmt"/>
        <line num="460" count="1" type="stmt"/>
        <line num="461" count="1" type="stmt"/>
        <line num="463" count="1" type="stmt"/>
        <line num="464" count="1" type="stmt"/>
        <line num="465" count="1" type="stmt"/>
        <line num="466" count="1" type="stmt"/>
        <line num="467" count="1" type="stmt"/>
        <line num="468" count="1" type="stmt"/>
        <line num="470" count="1" type="stmt"/>
        <line num="471" count="1" type="stmt"/>
        <line num="472" count="1" type="stmt"/>
        <line num="473" count="1" type="stmt"/>
        <line num="474" count="1" type="stmt"/>
        <line num="475" count="1" type="stmt"/>
        <line num="476" count="1" type="stmt"/>
        <line num="477" count="1" type="stmt"/>
        <line num="479" count="1" type="stmt"/>
        <line num="480" count="1" type="stmt"/>
        <line num="481" count="1" type="stmt"/>
        <line num="482" count="1" type="stmt"/>
        <line num="483" count="1" type="stmt"/>
        <line num="484" count="1" type="stmt"/>
        <line num="485" count="1" type="stmt"/>
        <line num="486" count="1" type="stmt"/>
        <line num="487" count="1" type="stmt"/>
        <line num="488" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.views.auth">
      <metrics statements="834" coveredstatements="637" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="EditProfilePicture.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/auth/EditProfilePicture.vue">
        <metrics statements="96" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
      </file>
      <file name="LinkAccounts.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/auth/LinkAccounts.vue">
        <metrics statements="101" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
      </file>
      <file name="Login.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/auth/Login.vue">
        <metrics statements="132" coveredstatements="132" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
      </file>
      <file name="Profile.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/auth/Profile.vue">
        <metrics statements="367" coveredstatements="367" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="234" count="1" type="stmt"/>
        <line num="235" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="238" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="241" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="244" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="248" count="1" type="stmt"/>
        <line num="249" count="1" type="stmt"/>
        <line num="250" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="252" count="1" type="stmt"/>
        <line num="253" count="1" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="255" count="1" type="stmt"/>
        <line num="256" count="1" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="259" count="1" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="262" count="1" type="stmt"/>
        <line num="263" count="1" type="stmt"/>
        <line num="264" count="1" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="267" count="1" type="stmt"/>
        <line num="268" count="1" type="stmt"/>
        <line num="269" count="1" type="stmt"/>
        <line num="270" count="1" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="272" count="1" type="stmt"/>
        <line num="273" count="1" type="stmt"/>
        <line num="275" count="1" type="stmt"/>
        <line num="276" count="1" type="stmt"/>
        <line num="277" count="1" type="stmt"/>
        <line num="278" count="1" type="stmt"/>
        <line num="279" count="1" type="stmt"/>
        <line num="280" count="1" type="stmt"/>
        <line num="281" count="1" type="stmt"/>
        <line num="282" count="1" type="stmt"/>
        <line num="283" count="1" type="stmt"/>
        <line num="284" count="1" type="stmt"/>
        <line num="285" count="1" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="288" count="1" type="stmt"/>
        <line num="289" count="1" type="stmt"/>
        <line num="290" count="1" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
        <line num="293" count="1" type="stmt"/>
        <line num="294" count="1" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="296" count="1" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
        <line num="298" count="1" type="stmt"/>
        <line num="300" count="1" type="stmt"/>
        <line num="301" count="1" type="stmt"/>
        <line num="302" count="1" type="stmt"/>
        <line num="303" count="1" type="stmt"/>
        <line num="304" count="1" type="stmt"/>
        <line num="305" count="1" type="stmt"/>
        <line num="306" count="1" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="308" count="1" type="stmt"/>
        <line num="312" count="1" type="stmt"/>
        <line num="313" count="1" type="stmt"/>
        <line num="314" count="1" type="stmt"/>
        <line num="316" count="1" type="stmt"/>
        <line num="318" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
        <line num="320" count="1" type="stmt"/>
        <line num="322" count="1" type="stmt"/>
        <line num="323" count="1" type="stmt"/>
        <line num="324" count="1" type="stmt"/>
        <line num="326" count="1" type="stmt"/>
        <line num="327" count="1" type="stmt"/>
        <line num="328" count="1" type="stmt"/>
        <line num="329" count="1" type="stmt"/>
        <line num="330" count="1" type="stmt"/>
        <line num="331" count="1" type="stmt"/>
        <line num="332" count="1" type="stmt"/>
        <line num="333" count="1" type="stmt"/>
        <line num="334" count="1" type="stmt"/>
        <line num="335" count="1" type="stmt"/>
        <line num="336" count="1" type="stmt"/>
        <line num="337" count="1" type="stmt"/>
        <line num="339" count="1" type="stmt"/>
        <line num="340" count="1" type="stmt"/>
        <line num="341" count="1" type="stmt"/>
        <line num="342" count="1" type="stmt"/>
        <line num="343" count="1" type="stmt"/>
        <line num="345" count="1" type="stmt"/>
        <line num="346" count="1" type="stmt"/>
        <line num="347" count="1" type="stmt"/>
        <line num="348" count="1" type="stmt"/>
        <line num="349" count="1" type="stmt"/>
        <line num="350" count="1" type="stmt"/>
        <line num="351" count="1" type="stmt"/>
        <line num="353" count="1" type="stmt"/>
        <line num="354" count="1" type="stmt"/>
        <line num="355" count="1" type="stmt"/>
        <line num="356" count="1" type="stmt"/>
        <line num="357" count="1" type="stmt"/>
        <line num="358" count="1" type="stmt"/>
        <line num="359" count="1" type="stmt"/>
        <line num="360" count="1" type="stmt"/>
        <line num="361" count="1" type="stmt"/>
        <line num="362" count="1" type="stmt"/>
        <line num="363" count="1" type="stmt"/>
        <line num="364" count="1" type="stmt"/>
        <line num="365" count="1" type="stmt"/>
        <line num="366" count="1" type="stmt"/>
        <line num="367" count="1" type="stmt"/>
        <line num="368" count="1" type="stmt"/>
        <line num="370" count="1" type="stmt"/>
        <line num="371" count="1" type="stmt"/>
        <line num="373" count="1" type="stmt"/>
        <line num="374" count="1" type="stmt"/>
        <line num="376" count="1" type="stmt"/>
        <line num="377" count="1" type="stmt"/>
        <line num="378" count="1" type="stmt"/>
        <line num="379" count="1" type="stmt"/>
        <line num="380" count="1" type="stmt"/>
        <line num="381" count="1" type="stmt"/>
        <line num="382" count="1" type="stmt"/>
        <line num="383" count="1" type="stmt"/>
        <line num="384" count="1" type="stmt"/>
        <line num="385" count="1" type="stmt"/>
        <line num="387" count="1" type="stmt"/>
        <line num="388" count="1" type="stmt"/>
        <line num="389" count="1" type="stmt"/>
        <line num="391" count="1" type="stmt"/>
        <line num="393" count="1" type="stmt"/>
        <line num="394" count="1" type="stmt"/>
        <line num="396" count="1" type="stmt"/>
        <line num="397" count="1" type="stmt"/>
        <line num="398" count="1" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="400" count="1" type="stmt"/>
        <line num="401" count="1" type="stmt"/>
        <line num="402" count="1" type="stmt"/>
        <line num="403" count="1" type="stmt"/>
        <line num="404" count="1" type="stmt"/>
        <line num="406" count="1" type="stmt"/>
        <line num="407" count="1" type="stmt"/>
        <line num="409" count="1" type="stmt"/>
        <line num="410" count="1" type="stmt"/>
        <line num="411" count="1" type="stmt"/>
        <line num="412" count="1" type="stmt"/>
        <line num="413" count="1" type="stmt"/>
        <line num="414" count="1" type="stmt"/>
        <line num="415" count="1" type="stmt"/>
        <line num="416" count="1" type="stmt"/>
        <line num="417" count="1" type="stmt"/>
      </file>
      <file name="Register.vue" path="/home/<USER>/Apps/kuroibara/frontend/app/src/views/auth/Register.vue">
        <metrics statements="138" coveredstatements="138" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
