name: Dependency Updates

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:

jobs:
  update-dependencies:
    name: Update Dependencies
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Update Python dependencies
        run: |
          cd backend
          pip install --upgrade pip pip-tools
          pip-compile --upgrade requirements.in || echo "No requirements.in found, skipping Python updates"

      - name: Update Node.js dependencies
        run: |
          cd frontend/app
          npm update
          npm audit fix || true

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v6
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore: update dependencies'
          title: 'chore: Automated dependency updates'
          body: |
            This PR contains automated dependency updates.
            
            ## Changes
            - Updated Python dependencies in backend/
            - Updated Node.js dependencies in frontend/app/
            
            Please review the changes and ensure all tests pass before merging.
          branch: chore/dependency-updates
          delete-branch: true
          labels: |
            dependencies
            automated
            chore
